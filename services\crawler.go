package services

import (
	"fmt"
	"log"
	"net/url"
	"strings"
	"time"

	"colly-help/models"

	"github.com/gocolly/colly/v2"
)

// CrawlerService Colly爬虫服务
type CrawlerService struct {
	collector *colly.Collector
	config    CrawlerConfig
}

// CrawlerConfig 爬虫配置
type CrawlerConfig struct {
	UserAgent      string        // 用户代理
	Timeout        time.Duration // 请求超时时间
	Delay          time.Duration // 请求间隔
	Parallelism    int           // 并发数
	EnableDebug    bool          // 是否启用调试
	AllowedDomains []string      // 允许的域名
}

// NewCrawlerService 创建新的爬虫服务实例
func NewCrawlerService(config CrawlerConfig) *CrawlerService {
	// 创建Colly收集器
	c := colly.NewCollector()

	// 配置用户代理
	if config.UserAgent != "" {
		c.UserAgent = config.UserAgent
	} else {
		c.UserAgent = "Colly-Debug-Tool/1.0"
	}

	// 配置超时时间
	if config.Timeout > 0 {
		c.SetRequestTimeout(config.Timeout)
	} else {
		c.SetRequestTimeout(30 * time.Second)
	}

	// 配置请求间隔
	if config.Delay > 0 {
		c.Limit(&colly.LimitRule{
			DomainGlob:  "*",
			Parallelism: config.Parallelism,
			Delay:       config.Delay,
		})
	}

	// 配置允许的域名
	if len(config.AllowedDomains) > 0 {
		c.AllowedDomains = config.AllowedDomains
	}

	// 启用调试模式
	if config.EnableDebug {
		c.OnRequest(func(r *colly.Request) {
			fmt.Printf("调试: 正在访问 %s\n", r.URL.String())
		})
	}

	// 错误处理
	c.OnError(func(r *colly.Response, err error) {
		log.Printf("请求失败 %s: %v", r.Request.URL, err)
	})

	// 请求前处理
	c.OnRequest(func(r *colly.Request) {
		log.Printf("正在访问: %s", r.URL.String())
	})

	// 响应处理
	c.OnResponse(func(r *colly.Response) {
		log.Printf("收到响应: %s (状态码: %d, 大小: %d bytes)",
			r.Request.URL, r.StatusCode, len(r.Body))
	})

	return &CrawlerService{
		collector: c,
		config:    config,
	}
}

// TestSelectors 测试CSS选择器
func (cs *CrawlerService) TestSelectors(request *models.TestRequest) (*models.TestResponse, error) {
	startTime := time.Now()

	response := &models.TestResponse{
		Success: true,
		Message: "测试完成",
		Results: make([]models.URLResult, 0),
		Stats: models.TestStats{
			TotalURLs:      len(request.URLs),
			TotalSelectors: len(request.Selectors),
		},
	}

	// 遍历每个URL进行测试
	for _, testURL := range request.URLs {
		urlResult := cs.testSingleURL(testURL, request.Selectors)
		response.Results = append(response.Results, urlResult)

		if urlResult.Success {
			response.Stats.SuccessURLs++
		}
	}

	// 计算统计信息
	for _, urlResult := range response.Results {
		for _, selectorResult := range urlResult.Results {
			response.Stats.TotalResults++
			if selectorResult.Success {
				response.Stats.SuccessResults++
			}
		}
	}

	// 计算处理时间
	response.Stats.ProcessingTime = int(time.Since(startTime).Milliseconds())

	// 如果没有任何成功的URL，标记整体失败
	if response.Stats.SuccessURLs == 0 {
		response.Success = false
		response.Message = "所有URL测试都失败了"
	}

	return response, nil
}

// testSingleURL 测试单个URL的所有选择器
func (cs *CrawlerService) testSingleURL(testURL string, selectors []models.Selector) models.URLResult {
	urlResult := models.URLResult{
		URL:     testURL,
		Success: true,
		Results: make([]models.SelectorResult, 0),
	}

	// 验证URL格式
	if _, err := url.Parse(testURL); err != nil {
		urlResult.Success = false
		urlResult.Error = fmt.Sprintf("无效的URL格式: %v", err)
		return urlResult
	}

	// 创建新的收集器用于此次测试（避免URL重复访问问题）
	tempCollector := colly.NewCollector()

	// 配置收集器
	tempCollector.UserAgent = cs.collector.UserAgent
	tempCollector.SetRequestTimeout(30 * time.Second)

	// 存储页面内容
	var pageHTML string
	var pageError error

	// 设置HTML处理器
	tempCollector.OnHTML("html", func(e *colly.HTMLElement) {
		pageHTML = string(e.Response.Body)
	})

	// 设置错误处理器
	tempCollector.OnError(func(r *colly.Response, err error) {
		pageError = err
	})

	// 访问URL
	err := tempCollector.Visit(testURL)
	if err != nil {
		urlResult.Success = false
		urlResult.Error = fmt.Sprintf("访问URL失败: %v", err)
		return urlResult
	}

	// 等待请求完成
	tempCollector.Wait()

	// 检查是否有页面错误
	if pageError != nil {
		urlResult.Success = false
		urlResult.Error = fmt.Sprintf("页面加载错误: %v", pageError)
		return urlResult
	}

	// 如果没有获取到HTML内容
	if pageHTML == "" {
		urlResult.Success = false
		urlResult.Error = "未能获取到页面内容"
		return urlResult
	}

	// 测试每个选择器
	hasSuccessSelector := false
	for _, selector := range selectors {
		selectorResult := cs.testSelector(testURL, pageHTML, selector)
		urlResult.Results = append(urlResult.Results, selectorResult)

		if selectorResult.Success {
			hasSuccessSelector = true
		}
	}

	// 如果没有任何选择器成功，标记URL失败
	if !hasSuccessSelector {
		urlResult.Success = false
		urlResult.Error = "所有选择器都未能匹配到内容"
	}

	return urlResult
}

// testSelector 测试单个选择器
func (cs *CrawlerService) testSelector(testURL, pageHTML string, selector models.Selector) models.SelectorResult {
	result := models.SelectorResult{
		SelectorID:   selector.ID,
		SelectorName: selector.Name,
		Selector:     selector.Selector,
		Success:      false,
		Count:        0,
	}

	// 创建新的收集器用于选择器测试
	tempCollector := colly.NewCollector()
	tempCollector.UserAgent = cs.collector.UserAgent

	var extractedData []string
	var elementCount int
	var hasError bool

	// 根据选择器类型设置处理器
	tempCollector.OnHTML(selector.Selector, func(e *colly.HTMLElement) {
		elementCount++

		var value string
		switch selector.Type {
		case "text":
			value = strings.TrimSpace(e.Text)
		case "html":
			// 获取元素的内部HTML内容
			value = strings.TrimSpace(e.Text)
		case "attr":
			if selector.Attribute != "" {
				value = e.Attr(selector.Attribute)
			}
		case "href":
			value = e.Attr("href")
			// 处理相对链接
			if value != "" && !strings.HasPrefix(value, "http") {
				if baseURL, err := url.Parse(testURL); err == nil {
					if absoluteURL, err := baseURL.Parse(value); err == nil {
						value = absoluteURL.String()
					}
				}
			}
		default:
			value = strings.TrimSpace(e.Text)
		}

		if value != "" {
			extractedData = append(extractedData, value)
		}
	})

	// 设置错误处理
	tempCollector.OnError(func(r *colly.Response, err error) {
		result.Error = fmt.Sprintf("选择器测试错误: %v", err)
		hasError = true
	})

	// 重新访问原始URL来测试选择器
	// 这样可以确保选择器在真实的HTML结构上工作
	err := tempCollector.Visit(testURL)
	if err != nil {
		result.Error = fmt.Sprintf("重新访问URL失败: %v", err)
		return result
	}

	// 等待请求完成
	tempCollector.Wait()

	// 设置结果
	result.Count = elementCount
	if hasError {
		// 如果有错误，结果已经在错误处理器中设置
		return result
	}

	if len(extractedData) > 0 {
		result.Success = true
		if len(extractedData) == 1 {
			result.Data = extractedData[0]
		} else {
			result.Data = extractedData
		}
	} else if elementCount > 0 {
		result.Success = true
		result.Data = fmt.Sprintf("找到 %d 个匹配元素，但未提取到文本内容", elementCount)
	} else {
		result.Error = "未找到匹配的元素"
	}

	return result
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() CrawlerConfig {
	return CrawlerConfig{
		UserAgent:   "Colly-Debug-Tool/1.0 (Compatible; Web Scraping Tool)",
		Timeout:     30 * time.Second,
		Delay:       1 * time.Second,
		Parallelism: 2,
		EnableDebug: false,
	}
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Colly调试工具 - CSS选择器测试平台</title>
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/components.css">
    <link rel="stylesheet" href="/static/css/responsive.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">
                    <span class="logo-icon">🕷️</span>
                    Colly调试工具
                </h1>
                <nav class="nav">
                    <button class="nav-btn active" data-tab="selector-test">选择器测试</button>
                    <button class="nav-btn" data-tab="scheme-manager">方案管理</button>
                    <button class="nav-btn" data-tab="list-detail">列表详情</button>
                </nav>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main">
        <div class="container">
            <!-- 选择器测试页面 -->
            <div id="selector-test" class="tab-content active">
                <div class="page-header">
                    <h2>CSS选择器测试</h2>
                    <p class="page-description">输入URL和CSS选择器，实时测试选择器的匹配效果</p>
                </div>

                <!-- URL输入区域 -->
                <section class="url-section">
                    <div class="section-header">
                        <h3>目标URL配置</h3>
                        <button class="btn btn-secondary" id="add-url-btn">+ 添加URL</button>
                    </div>
                    <div class="url-list" id="url-list">
                        <div class="url-item">
                            <input type="url" class="url-input" placeholder="请输入要测试的URL地址" required>
                            <button class="btn btn-danger btn-sm remove-url">删除</button>
                        </div>
                    </div>
                </section>

                <!-- 选择器配置区域 -->
                <section class="selector-section">
                    <div class="section-header">
                        <h3>CSS选择器配置</h3>
                        <button class="btn btn-secondary" id="add-selector-btn">+ 添加选择器</button>
                    </div>
                    <div class="selector-cards" id="selector-cards">
                        <div class="selector-card">
                            <div class="card-header">
                                <input type="text" class="selector-name" placeholder="选择器名称" value="标题">
                                <button class="btn btn-danger btn-sm remove-selector">删除</button>
                            </div>
                            <div class="card-body">
                                <input type="text" class="selector-input" placeholder="CSS选择器，如：h1, .title, #content" value="h1">
                                <select class="selector-type">
                                    <option value="text">提取文本</option>
                                    <option value="html">提取HTML</option>
                                    <option value="attr">提取属性</option>
                                    <option value="href">提取链接</option>
                                </select>
                                <input type="text" class="attr-name" placeholder="属性名称" style="display:none;">
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 测试控制区域 -->
                <section class="control-section">
                    <div class="control-buttons">
                        <button class="btn btn-primary btn-large" id="test-btn">
                            <span class="btn-icon">🔍</span>
                            开始测试
                        </button>
                        <button class="btn btn-secondary" id="clear-btn">清空结果</button>
                        <button class="btn btn-success" id="save-scheme-btn">保存方案</button>
                    </div>
                    <div class="test-status" id="test-status">
                        <span class="status-text">准备就绪</span>
                        <div class="progress-bar" style="display:none;">
                            <div class="progress-fill"></div>
                        </div>
                    </div>
                </section>

                <!-- 结果展示区域 -->
                <section class="results-section">
                    <div class="section-header">
                        <h3>测试结果</h3>
                        <div class="result-actions">
                            <button class="btn btn-outline" id="export-json-btn">导出JSON</button>
                            <button class="btn btn-outline" id="export-csv-btn">导出CSV</button>
                        </div>
                    </div>
                    <div class="results-container" id="results-container">
                        <div class="empty-state">
                            <div class="empty-icon">📊</div>
                            <p>暂无测试结果，请配置URL和选择器后点击"开始测试"</p>
                        </div>
                    </div>
                </section>
            </div>

            <!-- 方案管理页面 -->
            <div id="scheme-manager" class="tab-content">
                <div class="page-header">
                    <h2>方案管理</h2>
                    <p class="page-description">管理已保存的测试方案，支持导入导出</p>
                </div>
                <div class="scheme-list" id="scheme-list">
                    <div class="empty-state">
                        <div class="empty-icon">📁</div>
                        <p>暂无保存的方案</p>
                    </div>
                </div>
            </div>

            <!-- 列表详情页面 -->
            <div id="list-detail" class="tab-content">
                <div class="page-header">
                    <h2>列表详情爬取</h2>
                    <p class="page-description">配置列表页和详情页的联合爬取规则</p>
                </div>
                <div class="list-detail-config">
                    <p class="coming-soon">功能开发中...</p>
                </div>
            </div>
        </div>
    </main>

    <!-- 模态框 -->
    <div class="modal" id="save-scheme-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>保存测试方案</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="scheme-name">方案名称</label>
                    <input type="text" id="scheme-name" placeholder="请输入方案名称">
                </div>
                <div class="form-group">
                    <label for="scheme-description">方案描述</label>
                    <textarea id="scheme-description" placeholder="请输入方案描述（可选）"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancel-save">取消</button>
                <button class="btn btn-primary" id="confirm-save">保存</button>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="/static/js/utils.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/components.js"></script>
    <script src="/static/js/main.js"></script>
</body>
</html>

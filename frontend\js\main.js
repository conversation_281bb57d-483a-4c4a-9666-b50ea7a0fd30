/**
 * 主应用程序
 * 负责初始化和协调各个组件
 */

class CollyDebugApp {
    constructor() {
        this.currentTab = 'selector-test';
        this.components = {};
        this.init();
    }

    /**
     * 初始化应用程序
     */
    init() {
        this.initComponents();
        this.bindEvents();
        this.setupNotificationContainer();
        console.log('Colly调试工具已启动');
    }

    /**
     * 初始化组件
     */
    initComponents() {
        // URL管理器
        const urlContainer = Utils.DOM.$('#url-list');
        if (urlContainer) {
            this.urlManager = new Components.UrlManager(urlContainer);
        }

        // 选择器管理器
        const selectorContainer = Utils.DOM.$('#selector-cards');
        if (selectorContainer) {
            this.selectorManager = new Components.SelectorManager(selectorContainer);
        }

        // 结果展示器
        const resultsContainer = Utils.DOM.$('#results-container');
        if (resultsContainer) {
            this.resultsDisplay = new Components.ResultsDisplay(resultsContainer);
        }

        // 方案管理器
        const schemeContainer = Utils.DOM.$('#scheme-list');
        if (schemeContainer) {
            this.schemeManager = new Components.SchemeManager(schemeContainer);
        }

        // 保存方案模态框
        this.saveSchemeModal = new Components.Modal('save-scheme-modal');

        this.components = {
            urlManager: this.urlManager,
            selectorManager: this.selectorManager,
            resultsDisplay: this.resultsDisplay,
            schemeManager: this.schemeManager,
            saveSchemeModal: this.saveSchemeModal
        };
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 标签页切换
        const navBtns = Utils.DOM.$$('.nav-btn');
        navBtns.forEach(btn => {
            Utils.DOM.on(btn, 'click', (e) => {
                const tabId = e.target.dataset.tab;
                this.switchTab(tabId);
            });
        });

        // 测试按钮
        const testBtn = Utils.DOM.$('#test-btn');
        if (testBtn) {
            Utils.DOM.on(testBtn, 'click', () => this.runTest());
        }

        // 清空结果按钮
        const clearBtn = Utils.DOM.$('#clear-btn');
        if (clearBtn) {
            Utils.DOM.on(clearBtn, 'click', () => this.clearResults());
        }

        // 保存方案按钮
        const saveSchemeBtn = Utils.DOM.$('#save-scheme-btn');
        if (saveSchemeBtn) {
            Utils.DOM.on(saveSchemeBtn, 'click', () => this.showSaveSchemeModal());
        }

        // 保存方案模态框事件
        const cancelSaveBtn = Utils.DOM.$('#cancel-save');
        const confirmSaveBtn = Utils.DOM.$('#confirm-save');
        
        if (cancelSaveBtn) {
            Utils.DOM.on(cancelSaveBtn, 'click', () => this.saveSchemeModal.hide());
        }
        
        if (confirmSaveBtn) {
            Utils.DOM.on(confirmSaveBtn, 'click', () => this.saveScheme());
        }

        // 键盘快捷键
        Utils.DOM.on(document, 'keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'Enter':
                        e.preventDefault();
                        this.runTest();
                        break;
                    case 's':
                        e.preventDefault();
                        this.showSaveSchemeModal();
                        break;
                }
            }
        });
    }

    /**
     * 设置通知容器
     */
    setupNotificationContainer() {
        if (!Utils.DOM.$('.notification-container')) {
            const container = Utils.DOM.create('div', { className: 'notification-container' });
            document.body.appendChild(container);
        }
    }

    /**
     * 切换标签页
     * @param {string} tabId - 标签页ID
     */
    switchTab(tabId) {
        // 更新导航按钮状态
        const navBtns = Utils.DOM.$$('.nav-btn');
        navBtns.forEach(btn => {
            if (btn.dataset.tab === tabId) {
                Utils.DOM.addClass(btn, 'active');
            } else {
                Utils.DOM.removeClass(btn, 'active');
            }
        });

        // 更新内容区域
        const tabContents = Utils.DOM.$$('.tab-content');
        tabContents.forEach(content => {
            if (content.id === tabId) {
                Utils.DOM.addClass(content, 'active');
            } else {
                Utils.DOM.removeClass(content, 'active');
            }
        });

        this.currentTab = tabId;

        // 如果切换到方案管理页面，刷新方案列表
        if (tabId === 'scheme-manager' && this.schemeManager) {
            this.schemeManager.loadSchemes();
        }
    }

    /**
     * 运行测试
     */
    async runTest() {
        if (!this.urlManager || !this.selectorManager) {
            Utils.Notification.error('组件未初始化');
            return;
        }

        // 获取URL和选择器
        const urls = this.urlManager.getUrls();
        const selectors = this.selectorManager.getSelectors();

        // 验证输入
        if (urls.length === 0) {
            Utils.Notification.warning('请至少输入一个有效的URL');
            return;
        }

        if (selectors.length === 0) {
            Utils.Notification.warning('请至少配置一个选择器');
            return;
        }

        // 显示测试状态
        this.updateTestStatus('正在测试...', true);

        try {
            // 发送测试请求
            const testData = { urls, selectors };
            const response = await API.testSelectors(testData);

            // 显示结果
            if (this.resultsDisplay) {
                this.resultsDisplay.displayResults(response.results);
            }

            this.updateTestStatus('测试完成', false);
            Utils.Notification.success(`测试完成，共处理 ${urls.length} 个URL，${selectors.length} 个选择器`);

        } catch (error) {
            console.error('测试失败:', error);
            this.updateTestStatus('测试失败', false);
            Utils.Notification.error('测试失败: ' + error.message);
        }
    }

    /**
     * 更新测试状态
     * @param {string} message - 状态消息
     * @param {boolean} loading - 是否显示加载状态
     */
    updateTestStatus(message, loading = false) {
        const statusElement = Utils.DOM.$('#test-status');
        if (!statusElement) return;

        const statusText = Utils.DOM.$('.status-text', statusElement);
        const progressBar = Utils.DOM.$('.progress-bar', statusElement);

        if (statusText) {
            statusText.textContent = message;
        }

        if (progressBar) {
            progressBar.style.display = loading ? 'block' : 'none';
        }
    }

    /**
     * 清空结果
     */
    clearResults() {
        if (this.resultsDisplay) {
            this.resultsDisplay.displayResults([]);
            Utils.Notification.info('结果已清空');
        }
    }

    /**
     * 显示保存方案模态框
     */
    showSaveSchemeModal() {
        if (!this.urlManager || !this.selectorManager) {
            Utils.Notification.error('无法获取当前配置');
            return;
        }

        const urls = this.urlManager.getUrls();
        const selectors = this.selectorManager.getSelectors();

        if (urls.length === 0 || selectors.length === 0) {
            Utils.Notification.warning('请先配置URL和选择器');
            return;
        }

        // 清空表单
        const nameInput = Utils.DOM.$('#scheme-name');
        const descInput = Utils.DOM.$('#scheme-description');
        
        if (nameInput) nameInput.value = '';
        if (descInput) descInput.value = '';

        this.saveSchemeModal.show();
        
        // 聚焦到名称输入框
        if (nameInput) {
            setTimeout(() => nameInput.focus(), 100);
        }
    }

    /**
     * 保存方案
     */
    async saveScheme() {
        const nameInput = Utils.DOM.$('#scheme-name');
        const descInput = Utils.DOM.$('#scheme-description');

        if (!nameInput) return;

        const name = nameInput.value.trim();
        if (!name) {
            Utils.Notification.warning('请输入方案名称');
            nameInput.focus();
            return;
        }

        const urls = this.urlManager.getUrls();
        const selectors = this.selectorManager.getSelectors();

        const schemeData = {
            id: Utils.StringUtils.generateId('scheme'),
            name: name,
            description: descInput ? descInput.value.trim() : '',
            urls: urls,
            selectors: selectors,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        try {
            if (this.schemeManager) {
                await this.schemeManager.addScheme(schemeData);
            }
            this.saveSchemeModal.hide();
        } catch (error) {
            console.error('保存方案失败:', error);
        }
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new CollyDebugApp();
});

// 导出应用类
window.CollyDebugApp = CollyDebugApp;

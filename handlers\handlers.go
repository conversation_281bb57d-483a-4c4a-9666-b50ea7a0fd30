package handlers

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"colly-help/models"
	"colly-help/services"
	"colly-help/storage"

	"github.com/gin-gonic/gin"
)

// Handler HTTP请求处理器
type Handler struct {
	storage        *storage.JSONStorage
	crawlerService *services.CrawlerService
}

// NewHandler 创建新的处理器实例
func NewHandler(storage *storage.JSONStorage, crawlerService *services.CrawlerService) *Handler {
	return &Handler{
		storage:        storage,
		crawlerService: crawlerService,
	}
}

// HandleTestSelectors 处理测试选择器请求
func (h *Handler) HandleTestSelectors(c *gin.Context) {
	var request models.TestRequest

	// 解析请求体
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数格式错误",
			Error:   err.Error(),
		})
		return
	}

	// 验证请求参数
	if len(request.URLs) == 0 {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请至少提供一个URL",
			Error:   "URLs列表为空",
		})
		return
	}

	if len(request.Selectors) == 0 {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请至少提供一个选择器",
			Error:   "Selectors列表为空",
		})
		return
	}

	// 执行测试
	response, err := h.crawlerService.TestSelectors(&request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "测试执行失败",
			Error:   err.Error(),
		})
		return
	}

	// 保存测试结果
	if err := h.storage.SaveTestResult(response); err != nil {
		// 记录错误但不影响响应
		fmt.Printf("保存测试结果失败: %v\n", err)
	}

	c.JSON(http.StatusOK, response)
}

// HandleGetSchemes 处理获取所有方案请求
func (h *Handler) HandleGetSchemes(c *gin.Context) {
	schemes, err := h.storage.GetAllSchemes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取方案列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.SchemeListResponse{
		Success: true,
		Message: "获取方案列表成功",
		Data:    schemes,
	})
}

// HandleCreateScheme 处理创建方案请求
func (h *Handler) HandleCreateScheme(c *gin.Context) {
	var scheme models.Scheme

	// 解析请求体
	if err := c.ShouldBindJSON(&scheme); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数格式错误",
			Error:   err.Error(),
		})
		return
	}

	// 验证必填字段
	if scheme.Name == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "方案名称不能为空",
			Error:   "name字段为空",
		})
		return
	}

	if len(scheme.URLs) == 0 {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "URL列表不能为空",
			Error:   "urls字段为空",
		})
		return
	}

	if len(scheme.Selectors) == 0 {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "选择器列表不能为空",
			Error:   "selectors字段为空",
		})
		return
	}

	// 保存方案
	if err := h.storage.SaveScheme(&scheme); err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "保存方案失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.SchemeResponse{
		Success: true,
		Message: "方案创建成功",
		Data:    scheme,
	})
}

// HandleUpdateScheme 处理更新方案请求
func (h *Handler) HandleUpdateScheme(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "方案ID不能为空",
			Error:   "缺少id参数",
		})
		return
	}

	var scheme models.Scheme

	// 解析请求体
	if err := c.ShouldBindJSON(&scheme); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数格式错误",
			Error:   err.Error(),
		})
		return
	}

	// 设置ID
	scheme.ID = id

	// 更新方案
	if err := h.storage.UpdateScheme(&scheme); err != nil {
		if strings.Contains(err.Error(), "方案不存在") {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Success: false,
				Message: "方案不存在",
				Error:   err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Success: false,
				Message: "更新方案失败",
				Error:   err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SchemeResponse{
		Success: true,
		Message: "方案更新成功",
		Data:    scheme,
	})
}

// HandleDeleteScheme 处理删除方案请求
func (h *Handler) HandleDeleteScheme(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "方案ID不能为空",
			Error:   "缺少id参数",
		})
		return
	}

	// 删除方案
	if err := h.storage.DeleteScheme(id); err != nil {
		if strings.Contains(err.Error(), "方案不存在") {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Success: false,
				Message: "方案不存在",
				Error:   err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Success: false,
				Message: "删除方案失败",
				Error:   err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "方案删除成功",
		"id":      id,
	})
}

// HandleCrawlListDetail 处理列表详情爬取请求
func (h *Handler) HandleCrawlListDetail(c *gin.Context) {
	var request models.CrawlListDetailRequest

	// 解析请求体
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数格式错误",
			Error:   err.Error(),
		})
		return
	}

	// TODO: 实现列表详情爬取逻辑
	// 这里先返回一个占位响应
	c.JSON(http.StatusOK, models.CrawlListDetailResponse{
		Success: true,
		Message: "列表详情爬取功能正在开发中",
		Results: []models.ListDetailResult{},
		Stats: models.ListDetailStats{
			ProcessingTime: 0,
		},
	})
}

// HandleExport 处理导出请求
func (h *Handler) HandleExport(c *gin.Context) {
	format := c.Param("format")
	if format != "json" && format != "csv" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "不支持的导出格式",
			Error:   "支持的格式: json, csv",
		})
		return
	}

	var data interface{}

	// 解析请求体中的数据
	if err := c.ShouldBindJSON(&data); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求数据格式错误",
			Error:   err.Error(),
		})
		return
	}

	// 根据格式导出
	switch format {
	case "json":
		h.exportJSON(c, data)
	case "csv":
		h.exportCSV(c, data)
	}
}

// exportJSON 导出JSON格式
func (h *Handler) exportJSON(c *gin.Context, data interface{}) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "JSON序列化失败",
			Error:   err.Error(),
		})
		return
	}

	filename := fmt.Sprintf("colly_debug_export_%s.json", time.Now().Format("20060102_150405"))
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "application/json")
	c.Data(http.StatusOK, "application/json", jsonData)
}

// exportCSV 导出CSV格式
func (h *Handler) exportCSV(c *gin.Context, data interface{}) {
	// 这里需要根据数据结构转换为CSV格式
	// 简化实现，假设data是测试结果数组

	var csvData [][]string
	csvData = append(csvData, []string{"URL", "选择器名称", "选择器", "状态", "匹配数量", "结果内容"})

	// TODO: 根据实际数据结构实现CSV转换逻辑
	// 这里先添加一个示例行
	csvData = append(csvData, []string{"示例URL", "示例选择器", "h1", "成功", "1", "示例内容"})

	filename := fmt.Sprintf("colly_debug_export_%s.csv", time.Now().Format("20060102_150405"))
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "text/csv")

	c.Stream(func(w io.Writer) bool {
		writer := csv.NewWriter(w)
		defer writer.Flush()

		for _, row := range csvData {
			if err := writer.Write(row); err != nil {
				return false
			}
		}
		return false
	})
}

// HandleGetStatus 处理获取系统状态请求
func (h *Handler) HandleGetStatus(c *gin.Context) {
	stats, err := h.storage.GetStorageStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取系统状态失败",
			Error:   err.Error(),
		})
		return
	}

	status := gin.H{
		"success":   true,
		"message":   "系统运行正常",
		"timestamp": time.Now().Unix(),
		"storage":   stats,
		"version":   "1.0.0",
	}

	c.JSON(http.StatusOK, status)
}

/**
 * 工具函数库
 * 提供常用的辅助函数和工具方法
 */

// DOM操作工具
const DOM = {
    /**
     * 根据选择器查找元素
     * @param {string} selector - CSS选择器
     * @param {Element} parent - 父元素，默认为document
     * @returns {Element|null}
     */
    $(selector, parent = document) {
        return parent.querySelector(selector);
    },

    /**
     * 根据选择器查找所有元素
     * @param {string} selector - CSS选择器
     * @param {Element} parent - 父元素，默认为document
     * @returns {NodeList}
     */
    $$(selector, parent = document) {
        return parent.querySelectorAll(selector);
    },

    /**
     * 创建元素
     * @param {string} tag - 标签名
     * @param {Object} attrs - 属性对象
     * @param {string} content - 内容
     * @returns {Element}
     */
    create(tag, attrs = {}, content = '') {
        const element = document.createElement(tag);
        Object.entries(attrs).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'dataset') {
                Object.entries(value).forEach(([dataKey, dataValue]) => {
                    element.dataset[dataKey] = dataValue;
                });
            } else {
                element.setAttribute(key, value);
            }
        });
        if (content) {
            element.innerHTML = content;
        }
        return element;
    },

    /**
     * 添加事件监听器
     * @param {Element} element - 目标元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 选项
     */
    on(element, event, handler, options = {}) {
        element.addEventListener(event, handler, options);
    },

    /**
     * 移除事件监听器
     * @param {Element} element - 目标元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    off(element, event, handler) {
        element.removeEventListener(event, handler);
    },

    /**
     * 切换类名
     * @param {Element} element - 目标元素
     * @param {string} className - 类名
     */
    toggleClass(element, className) {
        element.classList.toggle(className);
    },

    /**
     * 添加类名
     * @param {Element} element - 目标元素
     * @param {string} className - 类名
     */
    addClass(element, className) {
        element.classList.add(className);
    },

    /**
     * 移除类名
     * @param {Element} element - 目标元素
     * @param {string} className - 类名
     */
    removeClass(element, className) {
        element.classList.remove(className);
    }
};

// 字符串工具
const StringUtils = {
    /**
     * 生成唯一ID
     * @param {string} prefix - 前缀
     * @returns {string}
     */
    generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    /**
     * 截断字符串
     * @param {string} str - 原字符串
     * @param {number} length - 最大长度
     * @param {string} suffix - 后缀
     * @returns {string}
     */
    truncate(str, length = 50, suffix = '...') {
        if (str.length <= length) return str;
        return str.substring(0, length - suffix.length) + suffix;
    },

    /**
     * 转义HTML字符
     * @param {string} str - 原字符串
     * @returns {string}
     */
    escapeHtml(str) {
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    },

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string}
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};

// 验证工具
const Validator = {
    /**
     * 验证URL格式
     * @param {string} url - URL字符串
     * @returns {boolean}
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    /**
     * 验证CSS选择器格式
     * @param {string} selector - CSS选择器
     * @returns {boolean}
     */
    isValidSelector(selector) {
        try {
            document.querySelector(selector);
            return true;
        } catch {
            return false;
        }
    },

    /**
     * 验证必填字段
     * @param {string} value - 值
     * @returns {boolean}
     */
    isRequired(value) {
        return value && value.trim().length > 0;
    },

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean}
     */
    isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }
};

// 本地存储工具
const Storage = {
    /**
     * 设置本地存储
     * @param {string} key - 键名
     * @param {any} value - 值
     */
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('存储数据失败:', error);
        }
    },

    /**
     * 获取本地存储
     * @param {string} key - 键名
     * @param {any} defaultValue - 默认值
     * @returns {any}
     */
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('读取数据失败:', error);
            return defaultValue;
        }
    },

    /**
     * 删除本地存储
     * @param {string} key - 键名
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('删除数据失败:', error);
        }
    },

    /**
     * 清空本地存储
     */
    clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('清空数据失败:', error);
        }
    }
};

// 时间工具
const TimeUtils = {
    /**
     * 格式化时间
     * @param {Date|string|number} date - 时间
     * @param {string} format - 格式
     * @returns {string}
     */
    format(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    /**
     * 获取相对时间
     * @param {Date|string|number} date - 时间
     * @returns {string}
     */
    relative(date) {
        const now = new Date();
        const target = new Date(date);
        const diff = now - target;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days}天前`;
        if (hours > 0) return `${hours}小时前`;
        if (minutes > 0) return `${minutes}分钟前`;
        return '刚刚';
    }
};

// 防抖和节流工具
const Throttle = {
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间
     * @returns {Function}
     */
    debounce(func, delay = 300) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} delay - 延迟时间
     * @returns {Function}
     */
    throttle(func, delay = 300) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }
};

// 通知工具
const Notification = {
    /**
     * 显示成功消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长
     */
    success(message, duration = 3000) {
        this.show(message, 'success', duration);
    },

    /**
     * 显示错误消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长
     */
    error(message, duration = 5000) {
        this.show(message, 'error', duration);
    },

    /**
     * 显示警告消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长
     */
    warning(message, duration = 4000) {
        this.show(message, 'warning', duration);
    },

    /**
     * 显示信息消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长
     */
    info(message, duration = 3000) {
        this.show(message, 'info', duration);
    },

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     * @param {number} duration - 显示时长
     */
    show(message, type = 'info', duration = 3000) {
        // 创建通知容器（如果不存在）
        let container = DOM.$('.notification-container');
        if (!container) {
            container = DOM.create('div', { className: 'notification-container' });
            document.body.appendChild(container);
        }

        // 创建通知元素
        const notification = DOM.create('div', {
            className: `notification notification-${type} fade-in`
        }, `
            <span class="notification-message">${StringUtils.escapeHtml(message)}</span>
            <button class="notification-close">&times;</button>
        `);

        // 添加到容器
        container.appendChild(notification);

        // 绑定关闭事件
        const closeBtn = DOM.$('.notification-close', notification);
        DOM.on(closeBtn, 'click', () => {
            this.remove(notification);
        });

        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }
    },

    /**
     * 移除通知
     * @param {Element} notification - 通知元素
     */
    remove(notification) {
        if (notification && notification.parentNode) {
            DOM.addClass(notification, 'fade-out');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }
};

// 导出工具对象
window.Utils = {
    DOM,
    StringUtils,
    Validator,
    Storage,
    TimeUtils,
    Throttle,
    Notification
};

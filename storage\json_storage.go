package storage

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sort"
	"time"

	"colly-help/models"
)

// JSONStorage JSON文件存储实现
type JSONStorage struct {
	dataDir string // 数据存储目录
}

// NewJSONStorage 创建新的JSON存储实例
func NewJSONStorage(dataDir string) *JSONStorage {
	// 确保数据目录存在
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		panic(fmt.Sprintf("无法创建数据目录: %v", err))
	}

	return &JSONStorage{
		dataDir: dataDir,
	}
}

// SaveScheme 保存测试方案
func (s *JSONStorage) SaveScheme(scheme *models.Scheme) error {
	// 设置时间戳
	now := time.Now()
	if scheme.CreatedAt.IsZero() {
		scheme.CreatedAt = now
	}
	scheme.UpdatedAt = now

	// 构建文件路径
	filename := fmt.Sprintf("scheme_%s.json", scheme.ID)
	filePath := filepath.Join(s.dataDir, "schemes", filename)

	// 确保schemes目录存在
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return fmt.Errorf("无法创建schemes目录: %v", err)
	}

	// 序列化为JSON
	data, err := json.MarshalIndent(scheme, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化方案失败: %v", err)
	}

	// 写入文件
	if err := ioutil.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("写入方案文件失败: %v", err)
	}

	return nil
}

// GetScheme 获取指定ID的测试方案
func (s *JSONStorage) GetScheme(id string) (*models.Scheme, error) {
	filename := fmt.Sprintf("scheme_%s.json", id)
	filePath := filepath.Join(s.dataDir, "schemes", filename)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("方案不存在: %s", id)
	}

	// 读取文件
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取方案文件失败: %v", err)
	}

	// 反序列化
	var scheme models.Scheme
	if err := json.Unmarshal(data, &scheme); err != nil {
		return nil, fmt.Errorf("解析方案文件失败: %v", err)
	}

	return &scheme, nil
}

// GetAllSchemes 获取所有测试方案
func (s *JSONStorage) GetAllSchemes() ([]models.Scheme, error) {
	schemesDir := filepath.Join(s.dataDir, "schemes")

	// 检查schemes目录是否存在
	if _, err := os.Stat(schemesDir); os.IsNotExist(err) {
		return []models.Scheme{}, nil
	}

	// 读取目录中的所有文件
	files, err := ioutil.ReadDir(schemesDir)
	if err != nil {
		return nil, fmt.Errorf("读取schemes目录失败: %v", err)
	}

	var schemes []models.Scheme
	for _, file := range files {
		// 只处理JSON文件
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			filePath := filepath.Join(schemesDir, file.Name())

			// 读取文件
			data, err := ioutil.ReadFile(filePath)
			if err != nil {
				continue // 跳过无法读取的文件
			}

			// 反序列化
			var scheme models.Scheme
			if err := json.Unmarshal(data, &scheme); err != nil {
				continue // 跳过无法解析的文件
			}

			schemes = append(schemes, scheme)
		}
	}

	// 按创建时间倒序排列
	sort.Slice(schemes, func(i, j int) bool {
		return schemes[i].CreatedAt.After(schemes[j].CreatedAt)
	})

	return schemes, nil
}

// UpdateScheme 更新测试方案
func (s *JSONStorage) UpdateScheme(scheme *models.Scheme) error {
	// 检查方案是否存在
	existing, err := s.GetScheme(scheme.ID)
	if err != nil {
		return err
	}

	// 保留创建时间，更新修改时间
	scheme.CreatedAt = existing.CreatedAt
	scheme.UpdatedAt = time.Now()

	// 保存更新后的方案
	return s.SaveScheme(scheme)
}

// DeleteScheme 删除测试方案
func (s *JSONStorage) DeleteScheme(id string) error {
	filename := fmt.Sprintf("scheme_%s.json", id)
	filePath := filepath.Join(s.dataDir, "schemes", filename)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("方案不存在: %s", id)
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		return fmt.Errorf("删除方案文件失败: %v", err)
	}

	return nil
}

// SaveTestResult 保存测试结果
func (s *JSONStorage) SaveTestResult(result *models.TestResponse) error {
	// 构建文件名（使用时间戳）
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("test_result_%s.json", timestamp)
	filePath := filepath.Join(s.dataDir, "results", filename)

	// 确保results目录存在
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return fmt.Errorf("无法创建results目录: %v", err)
	}

	// 序列化为JSON
	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化测试结果失败: %v", err)
	}

	// 写入文件
	if err := ioutil.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("写入测试结果文件失败: %v", err)
	}

	return nil
}

// GetTestResults 获取测试结果历史
func (s *JSONStorage) GetTestResults(limit int) ([]models.TestResponse, error) {
	resultsDir := filepath.Join(s.dataDir, "results")

	// 检查results目录是否存在
	if _, err := os.Stat(resultsDir); os.IsNotExist(err) {
		return []models.TestResponse{}, nil
	}

	// 读取目录中的所有文件
	files, err := ioutil.ReadDir(resultsDir)
	if err != nil {
		return nil, fmt.Errorf("读取results目录失败: %v", err)
	}

	// 按修改时间倒序排列
	sort.Slice(files, func(i, j int) bool {
		return files[i].ModTime().After(files[j].ModTime())
	})

	var results []models.TestResponse
	count := 0
	for _, file := range files {
		if count >= limit {
			break
		}

		// 只处理JSON文件
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			filePath := filepath.Join(resultsDir, file.Name())

			// 读取文件
			data, err := ioutil.ReadFile(filePath)
			if err != nil {
				continue // 跳过无法读取的文件
			}

			// 反序列化
			var result models.TestResponse
			if err := json.Unmarshal(data, &result); err != nil {
				continue // 跳过无法解析的文件
			}

			results = append(results, result)
			count++
		}
	}

	return results, nil
}

// CleanOldResults 清理旧的测试结果
func (s *JSONStorage) CleanOldResults(keepDays int) error {
	resultsDir := filepath.Join(s.dataDir, "results")

	// 检查results目录是否存在
	if _, err := os.Stat(resultsDir); os.IsNotExist(err) {
		return nil // 目录不存在，无需清理
	}

	// 计算截止时间
	cutoffTime := time.Now().AddDate(0, 0, -keepDays)

	// 读取目录中的所有文件
	files, err := ioutil.ReadDir(resultsDir)
	if err != nil {
		return fmt.Errorf("读取results目录失败: %v", err)
	}

	deletedCount := 0
	for _, file := range files {
		if !file.IsDir() && file.ModTime().Before(cutoffTime) {
			filePath := filepath.Join(resultsDir, file.Name())
			if err := os.Remove(filePath); err == nil {
				deletedCount++
			}
		}
	}

	fmt.Printf("清理了 %d 个旧的测试结果文件\n", deletedCount)
	return nil
}

// GetStorageStats 获取存储统计信息
func (s *JSONStorage) GetStorageStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 统计方案数量
	schemes, err := s.GetAllSchemes()
	if err != nil {
		return nil, err
	}
	stats["schemeCount"] = len(schemes)

	// 统计结果数量
	results, err := s.GetTestResults(1000) // 最多统计1000个结果
	if err != nil {
		return nil, err
	}
	stats["resultCount"] = len(results)

	// 计算存储目录大小
	var totalSize int64
	err = filepath.Walk(s.dataDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			totalSize += info.Size()
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	stats["totalSize"] = totalSize

	return stats, nil
}

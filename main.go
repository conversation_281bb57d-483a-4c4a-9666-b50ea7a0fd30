package main

import (
	"log"

	"colly-help/handlers"
	"colly-help/services"
	"colly-help/storage"

	"github.com/gin-gonic/gin"
)

func main() {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 初始化存储服务
	jsonStorage := storage.NewJSONStorage("./data")

	// 初始化爬虫服务
	crawlerConfig := services.GetDefaultConfig()
	crawlerService := services.NewCrawlerService(crawlerConfig)

	// 初始化处理器
	handler := handlers.NewHandler(jsonStorage, crawlerService)

	// 创建Gin路由器
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 静态文件服务
	r.Static("/static", "./frontend")
	r.StaticFile("/", "./frontend/index.html")

	// API路由组
	api := r.Group("/api")
	{
		// 测试选择器接口
		api.POST("/test-selectors", handler.HandleTestSelectors)

		// 方案管理接口
		api.GET("/schemes", handler.HandleGetSchemes)
		api.POST("/schemes", handler.HandleCreateScheme)
		api.PUT("/schemes/:id", handler.HandleUpdateScheme)
		api.DELETE("/schemes/:id", handler.HandleDeleteScheme)

		// 列表+详情页联合爬取接口
		api.POST("/crawl-list-detail", handler.HandleCrawlListDetail)

		// 结果导出接口
		api.POST("/export/:format", handler.HandleExport)

		// 系统状态接口
		api.GET("/status", handler.HandleGetStatus)
	}

	// 启动服务器
	log.Println("🕷️  Colly调试工具服务器启动成功!")
	log.Println("📍 访问地址: http://localhost:8080")
	log.Println("📁 数据存储目录: ./data")
	log.Println("🚀 准备就绪，开始调试CSS选择器吧!")

	if err := r.Run(":8080"); err != nil {
		log.Fatal("❌ 服务器启动失败:", err)
	}
}

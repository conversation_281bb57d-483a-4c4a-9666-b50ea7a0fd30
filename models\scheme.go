package models

import (
	"time"
)

// Selector 选择器配置结构
type Selector struct {
	ID        string `json:"id"`        // 选择器唯一标识
	Name      string `json:"name"`      // 选择器名称
	Selector  string `json:"selector"`  // CSS选择器字符串
	Type      string `json:"type"`      // 提取类型：text, html, attr, href
	Attribute string `json:"attribute"` // 当type为attr时的属性名
}

// Scheme 测试方案结构
type Scheme struct {
	ID          string     `json:"id"`          // 方案唯一标识
	Name        string     `json:"name"`        // 方案名称
	Description string     `json:"description"` // 方案描述
	URLs        []string   `json:"urls"`        // 目标URL列表
	Selectors   []Selector `json:"selectors"`   // 选择器配置列表
	CreatedAt   time.Time  `json:"createdAt"`   // 创建时间
	UpdatedAt   time.Time  `json:"updatedAt"`   // 更新时间
}

// TestRequest 测试请求结构
type TestRequest struct {
	URLs      []string   `json:"urls"`      // 要测试的URL列表
	Selectors []Selector `json:"selectors"` // 选择器配置列表
}

// SelectorResult 单个选择器的测试结果
type SelectorResult struct {
	SelectorID   string      `json:"selectorId"`   // 选择器ID
	SelectorName string      `json:"selectorName"` // 选择器名称
	Selector     string      `json:"selector"`     // CSS选择器
	Success      bool        `json:"success"`      // 是否成功
	Count        int         `json:"count"`        // 匹配元素数量
	Data         interface{} `json:"data"`         // 提取的数据
	Error        string      `json:"error"`        // 错误信息
}

// URLResult 单个URL的测试结果
type URLResult struct {
	URL     string           `json:"url"`     // 测试的URL
	Success bool             `json:"success"` // 整体是否成功
	Results []SelectorResult `json:"results"` // 各选择器的结果
	Error   string           `json:"error"`   // URL级别的错误
}

// TestResponse 测试响应结构
type TestResponse struct {
	Success bool        `json:"success"` // 整体测试是否成功
	Message string      `json:"message"` // 响应消息
	Results []URLResult `json:"results"` // 测试结果列表
	Stats   TestStats   `json:"stats"`   // 统计信息
}

// TestStats 测试统计信息
type TestStats struct {
	TotalURLs       int `json:"totalUrls"`       // 总URL数
	SuccessURLs     int `json:"successUrls"`     // 成功URL数
	TotalSelectors  int `json:"totalSelectors"`  // 总选择器数
	SuccessResults  int `json:"successResults"`  // 成功结果数
	TotalResults    int `json:"totalResults"`    // 总结果数
	ProcessingTime  int `json:"processingTime"`  // 处理时间(毫秒)
}

// SchemeListResponse 方案列表响应
type SchemeListResponse struct {
	Success bool     `json:"success"`
	Message string   `json:"message"`
	Data    []Scheme `json:"data"`
}

// SchemeResponse 单个方案响应
type SchemeResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    Scheme `json:"data"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Error   string `json:"error"`
}

// ListDetailConfig 列表详情页配置
type ListDetailConfig struct {
	ID          string                 `json:"id"`          // 配置ID
	Name        string                 `json:"name"`        // 配置名称
	Description string                 `json:"description"` // 配置描述
	ListConfig  ListPageConfig         `json:"listConfig"`  // 列表页配置
	DetailConfig DetailPageConfig      `json:"detailConfig"` // 详情页配置
	CreatedAt   time.Time              `json:"createdAt"`   // 创建时间
	UpdatedAt   time.Time              `json:"updatedAt"`   // 更新时间
}

// ListPageConfig 列表页配置
type ListPageConfig struct {
	URLs            []string   `json:"urls"`            // 列表页URL模式
	ItemSelector    string     `json:"itemSelector"`    // 列表项选择器
	LinkSelector    string     `json:"linkSelector"`    // 详情页链接选择器
	PaginationSelector string  `json:"paginationSelector"` // 分页选择器(可选)
	MaxPages        int        `json:"maxPages"`        // 最大页数限制
	Selectors       []Selector `json:"selectors"`       // 列表页数据选择器
}

// DetailPageConfig 详情页配置
type DetailPageConfig struct {
	Selectors    []Selector            `json:"selectors"`    // 详情页选择器
	CleaningRules []DataCleaningRule   `json:"cleaningRules"` // 数据清洗规则
}

// DataCleaningRule 数据清洗规则
type DataCleaningRule struct {
	Field     string `json:"field"`     // 字段名
	Type      string `json:"type"`      // 清洗类型：trim, replace, regex, format
	Pattern   string `json:"pattern"`   // 匹配模式(用于replace和regex)
	Replace   string `json:"replace"`   // 替换内容
	Required  bool   `json:"required"`  // 是否必填
}

// CrawlListDetailRequest 列表详情爬取请求
type CrawlListDetailRequest struct {
	Config ListDetailConfig `json:"config"` // 爬取配置
}

// CrawlListDetailResponse 列表详情爬取响应
type CrawlListDetailResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Results []ListDetailResult      `json:"results"`
	Stats   ListDetailStats         `json:"stats"`
}

// ListDetailResult 列表详情结果
type ListDetailResult struct {
	ListURL    string                 `json:"listUrl"`    // 列表页URL
	DetailURL  string                 `json:"detailUrl"`  // 详情页URL
	ListData   map[string]interface{} `json:"listData"`   // 列表页数据
	DetailData map[string]interface{} `json:"detailData"` // 详情页数据
	Success    bool                   `json:"success"`    // 是否成功
	Error      string                 `json:"error"`      // 错误信息
}

// ListDetailStats 列表详情统计
type ListDetailStats struct {
	TotalListPages   int `json:"totalListPages"`   // 总列表页数
	TotalDetailPages int `json:"totalDetailPages"` // 总详情页数
	SuccessCount     int `json:"successCount"`     // 成功数量
	ErrorCount       int `json:"errorCount"`       // 错误数量
	ProcessingTime   int `json:"processingTime"`   // 处理时间(毫秒)
}

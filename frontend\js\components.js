/**
 * 组件管理
 * 提供可复用的UI组件和交互逻辑
 */

// URL管理组件
class UrlManager {
    constructor(container) {
        this.container = container;
        this.urls = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadFromStorage();
    }

    bindEvents() {
        // 添加URL按钮事件
        const addBtn = Utils.DOM.$('#add-url-btn');
        Utils.DOM.on(addBtn, 'click', () => this.addUrl());

        // 委托事件处理删除按钮
        Utils.DOM.on(this.container, 'click', (e) => {
            if (e.target.classList.contains('remove-url')) {
                this.removeUrl(e.target.closest('.url-item'));
            }
        });

        // 委托事件处理URL输入变化
        Utils.DOM.on(this.container, 'input', (e) => {
            if (e.target.classList.contains('url-input')) {
                this.validateUrl(e.target);
                this.saveToStorage();
            }
        });
    }

    /**
     * 添加新的URL输入框
     * @param {string} url - 预填充的URL
     */
    addUrl(url = '') {
        const urlItem = Utils.DOM.create('div', { className: 'url-item fade-in' }, `
            <input type="url" class="url-input" placeholder="请输入要测试的URL地址" value="${url}" required>
            <button class="btn btn-danger btn-sm remove-url">删除</button>
        `);

        this.container.appendChild(urlItem);
        
        // 聚焦到新添加的输入框
        const input = Utils.DOM.$('.url-input', urlItem);
        input.focus();

        this.urls.push({ id: Utils.StringUtils.generateId('url'), url: url });
        this.saveToStorage();
    }

    /**
     * 删除URL输入框
     * @param {Element} urlItem - URL项元素
     */
    removeUrl(urlItem) {
        if (this.container.children.length <= 1) {
            Utils.Notification.warning('至少需要保留一个URL输入框');
            return;
        }

        Utils.DOM.addClass(urlItem, 'fade-out');
        setTimeout(() => {
            urlItem.remove();
            this.saveToStorage();
        }, 300);
    }

    /**
     * 验证URL格式
     * @param {Element} input - 输入框元素
     */
    validateUrl(input) {
        const url = input.value.trim();
        if (url && !Utils.Validator.isValidUrl(url)) {
            input.setCustomValidity('请输入有效的URL地址');
            Utils.DOM.addClass(input, 'error');
        } else {
            input.setCustomValidity('');
            Utils.DOM.removeClass(input, 'error');
        }
    }

    /**
     * 获取所有URL
     * @returns {Array<string>}
     */
    getUrls() {
        const inputs = Utils.DOM.$$('.url-input', this.container);
        return Array.from(inputs)
            .map(input => input.value.trim())
            .filter(url => url && Utils.Validator.isValidUrl(url));
    }

    /**
     * 设置URL列表
     * @param {Array<string>} urls - URL列表
     */
    setUrls(urls) {
        // 清空现有URL
        this.container.innerHTML = '';
        
        // 添加新URL
        if (urls.length === 0) {
            this.addUrl();
        } else {
            urls.forEach(url => this.addUrl(url));
        }
    }

    /**
     * 保存到本地存储
     */
    saveToStorage() {
        const urls = this.getUrls();
        Utils.Storage.set('colly_debug_urls', urls);
    }

    /**
     * 从本地存储加载
     */
    loadFromStorage() {
        const urls = Utils.Storage.get('colly_debug_urls', []);
        if (urls.length > 0) {
            this.setUrls(urls);
        } else {
            this.addUrl();
        }
    }
}

// 选择器管理组件
class SelectorManager {
    constructor(container) {
        this.container = container;
        this.selectors = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadFromStorage();
    }

    bindEvents() {
        // 添加选择器按钮事件
        const addBtn = Utils.DOM.$('#add-selector-btn');
        Utils.DOM.on(addBtn, 'click', () => this.addSelector());

        // 委托事件处理删除按钮
        Utils.DOM.on(this.container, 'click', (e) => {
            if (e.target.classList.contains('remove-selector')) {
                this.removeSelector(e.target.closest('.selector-card'));
            }
        });

        // 委托事件处理选择器类型变化
        Utils.DOM.on(this.container, 'change', (e) => {
            if (e.target.classList.contains('selector-type')) {
                this.handleTypeChange(e.target);
            }
        });

        // 委托事件处理输入变化
        Utils.DOM.on(this.container, 'input', Utils.Throttle.debounce(() => {
            this.saveToStorage();
        }, 500));
    }

    /**
     * 添加新的选择器卡片
     * @param {Object} selectorData - 选择器数据
     */
    addSelector(selectorData = {}) {
        const id = Utils.StringUtils.generateId('selector');
        const data = {
            id,
            name: selectorData.name || '新选择器',
            selector: selectorData.selector || '',
            type: selectorData.type || 'text',
            attribute: selectorData.attribute || ''
        };

        const selectorCard = Utils.DOM.create('div', { 
            className: 'selector-card fade-in',
            dataset: { selectorId: id }
        }, `
            <div class="card-header">
                <input type="text" class="selector-name" placeholder="选择器名称" value="${data.name}">
                <button class="btn btn-danger btn-sm remove-selector">删除</button>
            </div>
            <div class="card-body">
                <input type="text" class="selector-input" placeholder="CSS选择器，如：h1, .title, #content" value="${data.selector}">
                <select class="selector-type">
                    <option value="text" ${data.type === 'text' ? 'selected' : ''}>提取文本</option>
                    <option value="html" ${data.type === 'html' ? 'selected' : ''}>提取HTML</option>
                    <option value="attr" ${data.type === 'attr' ? 'selected' : ''}>提取属性</option>
                    <option value="href" ${data.type === 'href' ? 'selected' : ''}>提取链接</option>
                </select>
                <input type="text" class="attr-name" placeholder="属性名称" value="${data.attribute}" 
                       style="display: ${data.type === 'attr' ? 'block' : 'none'};">
            </div>
        `);

        this.container.appendChild(selectorCard);
        this.selectors.push(data);
        this.saveToStorage();

        // 聚焦到选择器输入框
        const input = Utils.DOM.$('.selector-input', selectorCard);
        input.focus();
    }

    /**
     * 删除选择器卡片
     * @param {Element} selectorCard - 选择器卡片元素
     */
    removeSelector(selectorCard) {
        if (this.container.children.length <= 1) {
            Utils.Notification.warning('至少需要保留一个选择器');
            return;
        }

        const selectorId = selectorCard.dataset.selectorId;
        this.selectors = this.selectors.filter(s => s.id !== selectorId);

        Utils.DOM.addClass(selectorCard, 'fade-out');
        setTimeout(() => {
            selectorCard.remove();
            this.saveToStorage();
        }, 300);
    }

    /**
     * 处理选择器类型变化
     * @param {Element} selectElement - 选择框元素
     */
    handleTypeChange(selectElement) {
        const card = selectElement.closest('.selector-card');
        const attrInput = Utils.DOM.$('.attr-name', card);
        
        if (selectElement.value === 'attr') {
            attrInput.style.display = 'block';
            attrInput.focus();
        } else {
            attrInput.style.display = 'none';
        }
        
        this.saveToStorage();
    }

    /**
     * 获取所有选择器配置
     * @returns {Array<Object>}
     */
    getSelectors() {
        const cards = Utils.DOM.$$('.selector-card', this.container);
        return Array.from(cards).map(card => {
            const nameInput = Utils.DOM.$('.selector-name', card);
            const selectorInput = Utils.DOM.$('.selector-input', card);
            const typeSelect = Utils.DOM.$('.selector-type', card);
            const attrInput = Utils.DOM.$('.attr-name', card);

            return {
                id: card.dataset.selectorId,
                name: nameInput.value.trim() || '未命名选择器',
                selector: selectorInput.value.trim(),
                type: typeSelect.value,
                attribute: typeSelect.value === 'attr' ? attrInput.value.trim() : ''
            };
        }).filter(s => s.selector); // 过滤掉空选择器
    }

    /**
     * 设置选择器列表
     * @param {Array<Object>} selectors - 选择器列表
     */
    setSelectors(selectors) {
        // 清空现有选择器
        this.container.innerHTML = '';
        this.selectors = [];
        
        // 添加新选择器
        if (selectors.length === 0) {
            this.addSelector();
        } else {
            selectors.forEach(selector => this.addSelector(selector));
        }
    }

    /**
     * 保存到本地存储
     */
    saveToStorage() {
        const selectors = this.getSelectors();
        Utils.Storage.set('colly_debug_selectors', selectors);
    }

    /**
     * 从本地存储加载
     */
    loadFromStorage() {
        const selectors = Utils.Storage.get('colly_debug_selectors', []);
        if (selectors.length > 0) {
            this.setSelectors(selectors);
        } else {
            // 添加默认选择器
            this.addSelector({
                name: '标题',
                selector: 'h1',
                type: 'text'
            });
        }
    }
}

// 结果展示组件
class ResultsDisplay {
    constructor(container) {
        this.container = container;
        this.results = [];
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 导出按钮事件
        const exportJsonBtn = Utils.DOM.$('#export-json-btn');
        const exportCsvBtn = Utils.DOM.$('#export-csv-btn');

        if (exportJsonBtn) {
            Utils.DOM.on(exportJsonBtn, 'click', () => this.exportResults('json'));
        }

        if (exportCsvBtn) {
            Utils.DOM.on(exportCsvBtn, 'click', () => this.exportResults('csv'));
        }
    }

    /**
     * 显示测试结果
     * @param {Array} results - 测试结果数组
     */
    displayResults(results) {
        this.results = results;

        if (!results || results.length === 0) {
            this.showEmptyState();
            return;
        }

        const table = this.createResultsTable(results);
        this.container.innerHTML = '';
        this.container.appendChild(table);
    }

    /**
     * 创建结果表格
     * @param {Array} results - 结果数据
     * @returns {Element}
     */
    createResultsTable(results) {
        const table = Utils.DOM.create('table', { className: 'results-table' });

        // 创建表头
        const thead = Utils.DOM.create('thead');
        const headerRow = Utils.DOM.create('tr');

        const headers = ['URL', '选择器名称', '选择器', '状态', '匹配数量', '结果内容', '操作'];
        headers.forEach(header => {
            const th = Utils.DOM.create('th', {}, header);
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // 创建表体
        const tbody = Utils.DOM.create('tbody');

        results.forEach(result => {
            const row = this.createResultRow(result);
            tbody.appendChild(row);
        });

        table.appendChild(tbody);
        return table;
    }

    /**
     * 创建结果行
     * @param {Object} result - 单个结果数据
     * @returns {Element}
     */
    createResultRow(result) {
        const row = Utils.DOM.create('tr');

        // URL列
        const urlCell = Utils.DOM.create('td');
        const urlLink = Utils.DOM.create('a', {
            href: result.url,
            target: '_blank',
            className: 'url-link'
        }, Utils.StringUtils.truncate(result.url, 30));
        urlCell.appendChild(urlLink);

        // 选择器名称列
        const nameCell = Utils.DOM.create('td', {}, result.selectorName);

        // 选择器列
        const selectorCell = Utils.DOM.create('td');
        const selectorCode = Utils.DOM.create('code', { className: 'selector-code' }, result.selector);
        selectorCell.appendChild(selectorCode);

        // 状态列
        const statusCell = Utils.DOM.create('td');
        const statusBadge = Utils.DOM.create('span', {
            className: `result-status ${result.success ? 'success' : 'error'}`
        }, result.success ? '✓ 成功' : '✗ 失败');
        statusCell.appendChild(statusBadge);

        // 匹配数量列
        const countCell = Utils.DOM.create('td', {}, result.count || 0);

        // 结果内容列
        const contentCell = Utils.DOM.create('td');
        if (result.success && result.data) {
            const contentDiv = Utils.DOM.create('div', { className: 'result-content' });
            contentDiv.textContent = Array.isArray(result.data) ? result.data.join('\n') : result.data;
            contentCell.appendChild(contentDiv);
        } else if (result.error) {
            const errorDiv = Utils.DOM.create('div', { className: 'result-error' }, result.error);
            contentCell.appendChild(errorDiv);
        }

        // 操作列
        const actionCell = Utils.DOM.create('td');
        const actionDiv = Utils.DOM.create('div', { className: 'result-actions' });

        if (result.success && result.data) {
            const copyBtn = Utils.DOM.create('button', {
                className: 'btn btn-outline btn-sm',
                title: '复制结果'
            }, '📋');

            Utils.DOM.on(copyBtn, 'click', () => {
                this.copyToClipboard(result.data);
            });

            actionDiv.appendChild(copyBtn);
        }

        actionCell.appendChild(actionDiv);

        // 添加所有列到行
        [urlCell, nameCell, selectorCell, statusCell, countCell, contentCell, actionCell].forEach(cell => {
            row.appendChild(cell);
        });

        return row;
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        this.container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📊</div>
                <p>暂无测试结果，请配置URL和选择器后点击"开始测试"</p>
            </div>
        `;
    }

    /**
     * 复制内容到剪贴板
     * @param {string|Array} content - 要复制的内容
     */
    async copyToClipboard(content) {
        try {
            const text = Array.isArray(content) ? content.join('\n') : content;
            await navigator.clipboard.writeText(text);
            Utils.Notification.success('内容已复制到剪贴板');
        } catch (error) {
            console.error('复制失败:', error);
            Utils.Notification.error('复制失败，请手动选择复制');
        }
    }

    /**
     * 导出结果
     * @param {string} format - 导出格式 (json/csv)
     */
    async exportResults(format) {
        if (!this.results || this.results.length === 0) {
            Utils.Notification.warning('没有可导出的结果');
            return;
        }

        try {
            const blob = await API.exportResults(format, this.results);
            const filename = `colly_debug_results_${Utils.TimeUtils.format(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.${format}`;
            API.downloadFile(blob, filename);
            Utils.Notification.success(`结果已导出为 ${format.toUpperCase()} 格式`);
        } catch (error) {
            console.error('导出失败:', error);
            Utils.Notification.error('导出失败，请稍后重试');
        }
    }
}

// 方案管理组件
class SchemeManager {
    constructor(container) {
        this.container = container;
        this.schemes = [];
        this.init();
    }

    init() {
        this.loadSchemes();
        this.bindEvents();
    }

    bindEvents() {
        // 委托事件处理方案操作
        Utils.DOM.on(this.container, 'click', (e) => {
            const schemeItem = e.target.closest('.scheme-item');
            if (!schemeItem) return;

            const schemeId = schemeItem.dataset.schemeId;

            if (e.target.classList.contains('load-scheme')) {
                this.loadScheme(schemeId);
            } else if (e.target.classList.contains('delete-scheme')) {
                this.deleteScheme(schemeId);
            } else if (e.target.classList.contains('export-scheme')) {
                this.exportScheme(schemeId);
            }
        });
    }

    /**
     * 加载所有方案
     */
    async loadSchemes() {
        try {
            const response = await API.getSchemes();
            this.schemes = response.data || [];
            this.renderSchemes();
        } catch (error) {
            console.error('加载方案失败:', error);
            this.showEmptyState();
        }
    }

    /**
     * 渲染方案列表
     */
    renderSchemes() {
        if (this.schemes.length === 0) {
            this.showEmptyState();
            return;
        }

        const schemeList = Utils.DOM.create('div', { className: 'scheme-list' });

        this.schemes.forEach(scheme => {
            const schemeItem = this.createSchemeItem(scheme);
            schemeList.appendChild(schemeItem);
        });

        this.container.innerHTML = '';
        this.container.appendChild(schemeList);
    }

    /**
     * 创建方案项
     * @param {Object} scheme - 方案数据
     * @returns {Element}
     */
    createSchemeItem(scheme) {
        const schemeItem = Utils.DOM.create('div', {
            className: 'scheme-item fade-in',
            dataset: { schemeId: scheme.id }
        }, `
            <div class="scheme-header">
                <h4 class="scheme-title">${Utils.StringUtils.escapeHtml(scheme.name)}</h4>
                <div class="scheme-actions">
                    <button class="btn btn-primary btn-sm load-scheme">加载</button>
                    <button class="btn btn-outline btn-sm export-scheme">导出</button>
                    <button class="btn btn-danger btn-sm delete-scheme">删除</button>
                </div>
            </div>
            <div class="scheme-meta">
                <span class="tag">URL: ${scheme.urls ? scheme.urls.length : 0}个</span>
                <span class="tag">选择器: ${scheme.selectors ? scheme.selectors.length : 0}个</span>
                <span class="tag primary">创建时间: ${Utils.TimeUtils.format(scheme.createdAt)}</span>
            </div>
            <div class="scheme-description">
                ${Utils.StringUtils.escapeHtml(scheme.description || '暂无描述')}
            </div>
        `);

        return schemeItem;
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        this.container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📁</div>
                <p>暂无保存的方案</p>
                <button class="btn btn-primary" onclick="window.app.switchTab('selector-test')">
                    去创建方案
                </button>
            </div>
        `;
    }

    /**
     * 加载方案到测试页面
     * @param {string} schemeId - 方案ID
     */
    async loadScheme(schemeId) {
        try {
            const scheme = this.schemes.find(s => s.id === schemeId);
            if (!scheme) {
                Utils.Notification.error('方案不存在');
                return;
            }

            // 切换到测试页面
            window.app.switchTab('selector-test');

            // 加载URL和选择器
            if (scheme.urls) {
                window.app.urlManager.setUrls(scheme.urls);
            }

            if (scheme.selectors) {
                window.app.selectorManager.setSelectors(scheme.selectors);
            }

            Utils.Notification.success(`方案 "${scheme.name}" 已加载`);
        } catch (error) {
            console.error('加载方案失败:', error);
            Utils.Notification.error('加载方案失败');
        }
    }

    /**
     * 删除方案
     * @param {string} schemeId - 方案ID
     */
    async deleteScheme(schemeId) {
        const scheme = this.schemes.find(s => s.id === schemeId);
        if (!scheme) return;

        if (!confirm(`确定要删除方案 "${scheme.name}" 吗？此操作不可撤销。`)) {
            return;
        }

        try {
            await API.deleteScheme(schemeId);
            this.schemes = this.schemes.filter(s => s.id !== schemeId);
            this.renderSchemes();
            Utils.Notification.success('方案已删除');
        } catch (error) {
            console.error('删除方案失败:', error);
            Utils.Notification.error('删除方案失败');
        }
    }

    /**
     * 导出方案
     * @param {string} schemeId - 方案ID
     */
    exportScheme(schemeId) {
        const scheme = this.schemes.find(s => s.id === schemeId);
        if (!scheme) return;

        const dataStr = JSON.stringify(scheme, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const filename = `colly_scheme_${scheme.name}_${Utils.TimeUtils.format(new Date(), 'YYYY-MM-DD')}.json`;

        API.downloadFile(blob, filename);
        Utils.Notification.success('方案已导出');
    }

    /**
     * 添加新方案
     * @param {Object} schemeData - 方案数据
     */
    async addScheme(schemeData) {
        try {
            const response = await API.createScheme(schemeData);
            this.schemes.push(response.data);
            this.renderSchemes();
            Utils.Notification.success('方案保存成功');
        } catch (error) {
            console.error('保存方案失败:', error);
            Utils.Notification.error('保存方案失败');
        }
    }
}

// 模态框组件
class Modal {
    constructor(modalId) {
        this.modal = Utils.DOM.$(`#${modalId}`);
        this.init();
    }

    init() {
        if (!this.modal) return;

        // 绑定关闭事件
        const closeBtn = Utils.DOM.$('.modal-close', this.modal);
        if (closeBtn) {
            Utils.DOM.on(closeBtn, 'click', () => this.hide());
        }

        // 点击背景关闭
        Utils.DOM.on(this.modal, 'click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });

        // ESC键关闭
        Utils.DOM.on(document, 'keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible()) {
                this.hide();
            }
        });
    }

    /**
     * 显示模态框
     */
    show() {
        if (this.modal) {
            Utils.DOM.addClass(this.modal, 'active');
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * 隐藏模态框
     */
    hide() {
        if (this.modal) {
            Utils.DOM.removeClass(this.modal, 'active');
            document.body.style.overflow = '';
        }
    }

    /**
     * 检查模态框是否可见
     * @returns {boolean}
     */
    isVisible() {
        return this.modal && this.modal.classList.contains('active');
    }
}

// 导出组件类
window.Components = {
    UrlManager,
    SelectorManager,
    ResultsDisplay,
    SchemeManager,
    Modal
};

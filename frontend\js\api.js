/**
 * API接口管理
 * 提供与后端通信的接口方法
 */

class ApiClient {
    constructor(baseUrl = '/api') {
        this.baseUrl = baseUrl;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }

    /**
     * 发送HTTP请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>}
     */
    async request(url, options = {}) {
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        try {
            const response = await fetch(`${this.baseUrl}${url}`, config);
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    /**
     * GET请求
     * @param {string} url - 请求URL
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>}
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl, { method: 'GET' });
    }

    /**
     * POST请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @returns {Promise<Object>}
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @returns {Promise<Object>}
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     * @param {string} url - 请求URL
     * @returns {Promise<Object>}
     */
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }

    // ========== 选择器测试相关接口 ==========

    /**
     * 测试CSS选择器
     * @param {Object} testData - 测试数据
     * @param {Array} testData.urls - URL列表
     * @param {Array} testData.selectors - 选择器列表
     * @returns {Promise<Object>}
     */
    async testSelectors(testData) {
        return this.post('/test-selectors', testData);
    }

    // ========== 方案管理相关接口 ==========

    /**
     * 获取所有方案
     * @returns {Promise<Array>}
     */
    async getSchemes() {
        return this.get('/schemes');
    }

    /**
     * 创建新方案
     * @param {Object} schemeData - 方案数据
     * @returns {Promise<Object>}
     */
    async createScheme(schemeData) {
        return this.post('/schemes', schemeData);
    }

    /**
     * 更新方案
     * @param {string} id - 方案ID
     * @param {Object} schemeData - 方案数据
     * @returns {Promise<Object>}
     */
    async updateScheme(id, schemeData) {
        return this.put(`/schemes/${id}`, schemeData);
    }

    /**
     * 删除方案
     * @param {string} id - 方案ID
     * @returns {Promise<Object>}
     */
    async deleteScheme(id) {
        return this.delete(`/schemes/${id}`);
    }

    // ========== 列表详情爬取相关接口 ==========

    /**
     * 执行列表+详情页联合爬取
     * @param {Object} crawlData - 爬取配置数据
     * @returns {Promise<Object>}
     */
    async crawlListDetail(crawlData) {
        return this.post('/crawl-list-detail', crawlData);
    }

    // ========== 导出相关接口 ==========

    /**
     * 导出结果为指定格式
     * @param {string} format - 导出格式 (json/csv)
     * @param {Object} data - 要导出的数据
     * @returns {Promise<Blob>}
     */
    async exportResults(format, data) {
        const response = await fetch(`${this.baseUrl}/export/${format}`, {
            method: 'POST',
            headers: this.defaultHeaders,
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`导出失败: ${response.status} ${response.statusText}`);
        }

        return response.blob();
    }
}

// 创建API客户端实例
const api = new ApiClient();

// 导出API方法的便捷函数
window.API = {
    // 选择器测试
    testSelectors: (testData) => api.testSelectors(testData),
    
    // 方案管理
    getSchemes: () => api.getSchemes(),
    createScheme: (schemeData) => api.createScheme(schemeData),
    updateScheme: (id, schemeData) => api.updateScheme(id, schemeData),
    deleteScheme: (id) => api.deleteScheme(id),
    
    // 列表详情爬取
    crawlListDetail: (crawlData) => api.crawlListDetail(crawlData),
    
    // 导出功能
    exportResults: (format, data) => api.exportResults(format, data),

    // 工具方法
    downloadFile: (blob, filename) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }
};

// 请求拦截器示例（可根据需要扩展）
class RequestInterceptor {
    constructor(apiClient) {
        this.apiClient = apiClient;
        this.setupInterceptors();
    }

    setupInterceptors() {
        // 保存原始request方法
        const originalRequest = this.apiClient.request.bind(this.apiClient);

        // 重写request方法
        this.apiClient.request = async (url, options = {}) => {
            // 请求前拦截
            this.beforeRequest(url, options);

            try {
                // 执行原始请求
                const response = await originalRequest(url, options);
                
                // 请求成功后拦截
                this.afterRequest(url, options, response);
                
                return response;
            } catch (error) {
                // 请求失败后拦截
                this.onError(url, options, error);
                throw error;
            }
        };
    }

    /**
     * 请求前拦截
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     */
    beforeRequest(url, options) {
        console.log(`发送请求: ${options.method || 'GET'} ${url}`);
        
        // 显示加载状态
        this.showLoading();
    }

    /**
     * 请求成功后拦截
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @param {Object} response - 响应数据
     */
    afterRequest(url, options, response) {
        console.log(`请求成功: ${options.method || 'GET'} ${url}`, response);
        
        // 隐藏加载状态
        this.hideLoading();
    }

    /**
     * 请求失败后拦截
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @param {Error} error - 错误对象
     */
    onError(url, options, error) {
        console.error(`请求失败: ${options.method || 'GET'} ${url}`, error);
        
        // 隐藏加载状态
        this.hideLoading();
        
        // 显示错误通知
        Utils.Notification.error(`请求失败: ${error.message}`);
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const loadingElement = Utils.DOM.$('.global-loading');
        if (loadingElement) {
            loadingElement.style.display = 'block';
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingElement = Utils.DOM.$('.global-loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }
}

// 初始化请求拦截器
new RequestInterceptor(api);

// 全局错误处理
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    Utils.Notification.error('发生了未知错误，请稍后重试');
});

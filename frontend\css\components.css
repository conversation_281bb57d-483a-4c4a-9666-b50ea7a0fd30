/* URL列表组件 */
.url-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.url-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.url-input {
    flex: 1;
    margin: 0;
}

.remove-url {
    flex-shrink: 0;
}

/* 选择器卡片组件 */
.selector-cards {
    display: grid;
    gap: var(--spacing-lg);
}

.selector-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}

.selector-card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.selector-name {
    flex: 1;
    margin: 0;
    font-weight: 500;
}

.card-body {
    padding: var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: var(--spacing-md);
    align-items: center;
}

.selector-input {
    margin: 0;
}

.selector-type {
    width: auto;
    min-width: 120px;
    margin: 0;
}

.attr-name {
    width: auto;
    min-width: 100px;
    margin: 0;
}

/* 控制按钮区域 */
.control-section {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.control-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* 结果表格 */
.results-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.results-table th,
.results-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.results-table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.results-table tr:hover {
    background: var(--bg-secondary);
}

.results-table td {
    vertical-align: top;
}

/* 结果状态标识 */
.result-status {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.result-status.success {
    background: rgb(34 197 94 / 0.1);
    color: var(--success-color);
}

.result-status.error {
    background: rgb(239 68 68 / 0.1);
    color: var(--danger-color);
}

.result-status.warning {
    background: rgb(245 158 11 / 0.1);
    color: var(--warning-color);
}

/* 结果内容 */
.result-content {
    max-width: 300px;
    max-height: 100px;
    overflow: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: var(--font-size-sm);
    background: var(--bg-secondary);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    white-space: pre-wrap;
    word-break: break-all;
}

/* 结果操作按钮 */
.result-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: auto;
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* 表单组 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

/* 方案列表 */
.scheme-list {
    display: grid;
    gap: var(--spacing-lg);
}

.scheme-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all 0.2s ease;
}

.scheme-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.scheme-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.scheme-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.scheme-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.scheme-meta {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.scheme-description {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 标签 */
.tag {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.tag.primary {
    background: rgb(37 99 235 / 0.1);
    color: var(--primary-color);
}

.tag.success {
    background: rgb(34 197 94 / 0.1);
    color: var(--success-color);
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
}

.stat-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* 即将推出 */
.coming-soon {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 2px dashed var(--border-color);
}

/* 通知组件 */
.notification-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-width: 400px;
}

.notification {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid;
    background: var(--bg-primary);
    min-height: 60px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.notification.fade-in {
    opacity: 1;
    transform: translateX(0);
}

.notification.fade-out {
    opacity: 0;
    transform: translateX(100%);
}

.notification-success {
    border-left-color: var(--success-color);
    background: rgb(34 197 94 / 0.05);
}

.notification-error {
    border-left-color: var(--danger-color);
    background: rgb(239 68 68 / 0.05);
}

.notification-warning {
    border-left-color: var(--warning-color);
    background: rgb(245 158 11 / 0.05);
}

.notification-info {
    border-left-color: var(--info-color);
    background: rgb(8 145 178 / 0.05);
}

.notification-message {
    flex: 1;
    font-weight: 500;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    margin-left: var(--spacing-sm);
}

.notification-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* 错误状态样式 */
.error {
    border-color: var(--danger-color) !important;
    box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1) !important;
}

/* 加载状态 */
.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--bg-secondary);
    z-index: 9999;
    display: none;
}

.global-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 30%;
    background: var(--primary-color);
    animation: loading-bar 2s infinite;
}

@keyframes loading-bar {
    0% { left: -30%; }
    100% { left: 100%; }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

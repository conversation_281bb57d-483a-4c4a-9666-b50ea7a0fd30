/* 响应式设计 - 移动优先 */

/* 超小屏幕 (手机竖屏) - 默认样式已适配 */

/* 小屏幕 (手机横屏) */
@media (max-width: 640px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-sm) 0;
    }
    
    .nav {
        width: 100%;
        justify-content: center;
    }
    
    .nav-btn {
        flex: 1;
        text-align: center;
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .main {
        padding: var(--spacing-md) 0;
    }
    
    section {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .section-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .url-item {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .card-body {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .control-buttons {
        flex-direction: column;
    }
    
    .btn-large {
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }
    
    .results-table {
        font-size: var(--font-size-sm);
    }
    
    .results-table th,
    .results-table td {
        padding: var(--spacing-sm);
    }
    
    .result-content {
        max-width: 200px;
        max-height: 80px;
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        margin: var(--spacing-sm);
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .scheme-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }
    
    .scheme-actions {
        justify-content: center;
    }
    
    .scheme-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* 中等屏幕 (平板竖屏) */
@media (min-width: 641px) and (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .nav {
        gap: var(--spacing-sm);
    }
    
    .nav-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .card-body {
        grid-template-columns: 1fr auto;
        gap: var(--spacing-sm);
    }
    
    .selector-type,
    .attr-name {
        grid-column: 1 / -1;
        margin-top: var(--spacing-sm);
    }
    
    .control-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .results-table {
        font-size: var(--font-size-sm);
    }
    
    .result-content {
        max-width: 250px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 大屏幕 (平板横屏/小桌面) */
@media (min-width: 769px) and (max-width: 1024px) {
    .selector-cards {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .scheme-list {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* 超大屏幕 (大桌面) */
@media (min-width: 1025px) {
    .container {
        max-width: 1400px;
    }
    
    .selector-cards {
        grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .scheme-list {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
    
    .results-table {
        font-size: var(--font-size-base);
    }
    
    .result-content {
        max-width: 400px;
        max-height: 120px;
    }
}

/* 高分辨率屏幕优化 */
@media (min-resolution: 2dppx) {
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* 打印样式 */
@media print {
    .header,
    .nav,
    .control-section,
    .result-actions,
    .scheme-actions,
    .modal {
        display: none !important;
    }
    
    body {
        background: white;
        color: black;
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .main {
        padding: 0;
    }
    
    section {
        box-shadow: none;
        border: 1px solid #ccc;
        margin-bottom: 20pt;
        page-break-inside: avoid;
    }
    
    .results-table {
        font-size: 10pt;
    }
    
    .results-table th,
    .results-table td {
        padding: 8pt;
        border: 1px solid #ccc;
    }
    
    .page-header h2 {
        font-size: 18pt;
        margin-bottom: 10pt;
    }
    
    .section-header h3 {
        font-size: 14pt;
        margin-bottom: 8pt;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1e293b;
        --bg-secondary: #0f172a;
        --bg-tertiary: #334155;
        --border-color: #475569;
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-muted: #94a3b8;
    }
    
    .results-table th {
        background: var(--bg-tertiary);
    }
    
    .modal {
        background: rgba(0, 0, 0, 0.8);
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-primary: #000000;
        --text-secondary: #333333;
    }
    
    .btn {
        border-width: 2px;
    }
    
    input, select, textarea {
        border-width: 2px;
    }
}

/* 横屏手机特殊处理 */
@media (max-height: 500px) and (orientation: landscape) {
    .header {
        position: static;
    }
    
    .main {
        padding: var(--spacing-sm) 0;
    }
    
    section {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
    }
    
    .page-header {
        margin-bottom: var(--spacing-md);
    }
    
    .modal-content {
        max-height: 95vh;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px;
        padding: var(--spacing-md);
    }
    
    .nav-btn {
        min-height: 44px;
    }
    
    input, select, textarea {
        min-height: 44px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .modal-close {
        min-width: 44px;
        min-height: 44px;
    }
    
    .tooltip:hover::after {
        display: none; /* 触摸设备不显示悬停提示 */
    }
}

/* 可访问性增强 */
@media (prefers-reduced-transparency: reduce) {
    .modal {
        backdrop-filter: none;
        background: rgba(0, 0, 0, 0.8);
    }
}

/* 焦点可见性增强 */
.btn:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}
